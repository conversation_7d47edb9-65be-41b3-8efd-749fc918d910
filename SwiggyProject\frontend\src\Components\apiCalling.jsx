import { useEffect } from "react";

const apiCalling = () => {
    useEffect(() =>{
        const fetchApi = async() => {
            try{
                const response = await fetch("https://www.swiggy.com/dapi/restaurants/list/v5?lat=21.99740&lng=79.00110&is-seo-homepage-enabled=true&page_type=DESKTOP_WEB_LISTING");

                const data = await response.json();
                console.log(data);
                return data;
            }
            catch(error){
                console.log("Catch block");
            };
        };
        fetchApi();

    }, []);
}

export default apiCalling;
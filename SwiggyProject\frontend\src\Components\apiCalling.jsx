import { useEffect, useState } from "react";

// ✅ Name follows custom hook convention
const useApiCalling = () => {
    const [restaurants, setRestaurants] = useState([]);

    useEffect(() => {
        const fetchApi = async () => {
            const response = await fetch(
                "https://www.swiggy.com/dapi/restaurants/list/v5?lat=27.49870&lng=77.66690&is-seo-homepage-enabled=true&page_type=DESKTOP_WEB_LISTING"
            );
            const data = await response.json();
            console.log("Full API Response:", data?.data?.cards[0]?.card?.card?.imageGridCards);
            setRestaurants(data?.data?.cards[0]?.card?.card?.imageGridCards);
        };

        console.log("API Calling");
        fetchApi();
    }, []);

    return restaurants;
};

export default useApiCalling;
